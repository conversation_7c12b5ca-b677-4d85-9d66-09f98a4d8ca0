# CMS Strapi (FishBush)

## Getting Started
Clone repository via command
```bash
git clone https://github.com/Student-Cyber-Games/CMS-FishBush
```

## Running via Docker compose
Copy `.env.example` to `.env` and fill in the following values:
```diff
-DATABASE_PASSWORD=
+DATABASE_PASSWORD=veryStrongPassword
```

Run `docker compose up --build` to start the server.

## Running locally via Strapi dev
Copy `.env.example` to `./fishbush/.env` and fill in the following values:
```diff
-DATABASE_HOST=
-DATABASE_PASSWORD=
+DATABASE_HOST=127.0.0.1
+DATABASE_PASSWORD=veryStrongPassword
```

Run `docker compose -f docker-compose.database.yml up --build` to start the database.

Run `cd fishbush && bun run dev` to start the server.

---

Go to http://localhost:1337/admin to access the admin panel.


## Development
Reference to the Strapi documentation: https://docs.strapi.io/developer-docs/latest/getting-started/introduction.html and OASIS documentation: https://student-cyber-games.github.io/OASIS-docs/