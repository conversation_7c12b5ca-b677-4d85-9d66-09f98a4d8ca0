services:
  fishbush_v5:
    container_name: fishbush_v5
    restart: unless-stopped
    build:
      context: ./fishbush
      dockerfile: Dockerfile.dev
    env_file: .env
    environment:
      HOST: ${HOST}
      PORT: ${PORT}

      PUBLIC_URL: ${PUBLIC_URL}
      NODE_ENV: ${NODE_ENV}

      APP_KEYS: ${STRAPI_APP_KEYS}
      API_TOKEN_SALT: ${STRAPI_API_TOKEN_SALT}
      ADMIN_JWT_SECRET: ${STRAPI_ADMIN_JWT_SECRET}
      TRANSFER_TOKEN_SALT: ${STRAPI_TRANSFER_TOKEN_SALT}
      JWT_SECRET: ${STRAPI_JWT_SECRET}
      
      GOOGLE_OAUTH_CLIENT_ID: ${GOOGLE_OAUTH_CLIENT_ID}
      GOOGLE_OAUTH_CLIENT_SECRET: ${GOOGLE_OAUTH_CLIENT_SECRET}
      GOOGLE_GSUITE_HD: ${GOOGLE_GSUITE_HD}

      DATABASE_CLIENT: ${DATABASE_CLIENT}
      DATABASE_HOST: ${DATABASE_HOST}
      DATABASE_PORT: ${DATABASE_PORT}
      DATABASE_NAME: ${DATABASE_NAME}
      DATABASE_USERNAME: ${DATABASE_USERNAME}
      DATABASE_PASSWORD: ${DATABASE_PASSWORD}
      DATABASE_SSL: ${DATABASE_SSL}
    volumes:
      - ./fishbush/config:/opt/app/config
      - ./fishbush/src:/opt/app/src
      - ./fishbush/package.json:/opt/package.json
      - ./fishbush/yarn.lock:/opt/yarn.lock
      - ./fishbush/public/uploads:/opt/app/public/uploads
    deploy:
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
        window: 120s
      resources:
        limits:
          cpus: '4'
          memory: 1g
        reservations:
          cpus: '0.2'
          memory: 500m
    ports:
      - "1337:1337"
    networks:
      - fishbush_network_v5
    depends_on:
      - fishbushdb_v5

  fishbushdb_v5:
    container_name: fishbushdb_v5
    hostname: ${DATABASE_HOST}
    restart: unless-stopped
    image: postgres:14
    env_file: .env
    environment:
      POSTGRES_USER: ${DATABASE_USERNAME}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
      POSTGRES_DB: ${DATABASE_NAME}
    deploy:
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
        window: 120s
      resources:
        limits:
          cpus: '1'
          memory: 1g
        reservations:
          cpus: '0.2'
          memory: 100m
    volumes:
      - strapi-DB-data-v5:/var/lib/postgresql/data/
    networks:
      - fishbush_network_v5
    ports:
      - "5432:5432"

volumes:
  strapi-DB-data-v5:

networks:
  fishbush_network_v5:
    name: fishbush_network_v5
    driver: bridge
