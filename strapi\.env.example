# Server Settings for Strapi
HOST=0.0.0.0
PORT=1337

# Strapi Public URL & Environment
PUBLIC_URL=http://localhost:1337
NODE_ENV=development

# Strapi Secrets and Keys
STRAPI_APP_KEYS=Y2gHBMI36NgBp/3bnyMqRg==,M8V6vfdFnTOulOF079RHiQ==,2Nr/WMnssVh1k6eszw/SPA==,x01cBO7+85suAv9pa5ri6g==
STRAPI_API_TOKEN_SALT=4S/eAC7QX2cs8REaZpbvqA==
STRAPI_ADMIN_JWT_SECRET=6Ltap3UKd2pWEbVy2QTwqQ==
STRAPI_TRANSFER_TOKEN_SALT=TfVnEsgZjkBLpIWTSEl7zA==
STRAPI_JWT_SECRET=6Ltap3UKd2pWEbVy2QTwqQ==

# Google OAuth Configuration
GOOGLE_OAUTH_CLIENT_ID=5f4b3b3b-7b1b-4b3b-8b3b-9b3b3b3b3b3b
GOOGLE_OAUTH_CLIENT_SECRET=5f4b3b3b-7b1b-4b3b-8b3b-9b3b3b3b3b3b
GOOGLE_GSUITE_HD=scg.cz

# Database Configuration
DATABASE_CLIENT=postgres
DATABASE_HOST=fishbushDB
DATABASE_PORT=5432
DATABASE_NAME=fishbushDB
DATABASE_USERNAME=db_admin
DATABASE_PASSWORD=
DATABASE_SSL=false
