diff --git a/node_modules/@strapi/content-type-builder/dist/admin/components/ContentTypeBuilderNav/ContentTypeBuilderNav.mjs b/node_modules/@strapi/content-type-builder/dist/admin/components/ContentTypeBuilderNav/ContentTypeBuilderNav.mjs
index 31c04db..68e7026 100644
--- a/node_modules/@strapi/content-type-builder/dist/admin/components/ContentTypeBuilderNav/ContentTypeBuilderNav.mjs
+++ b/node_modules/@strapi/content-type-builder/dist/admin/components/ContentTypeBuilderNav/ContentTypeBuilderNav.mjs
@@ -1,6 +1,16 @@
 import { jsxs, jsx } from 'react/jsx-runtime';
 import { Fragment } from 'react';
-import { SubNavLink, SubNav, SubNavHeader, SubNavSections, SubNavSection, SubNavLinkSection, Box, TextButton } from '@strapi/design-system';
+import {
+  SubNavLink,
+  SubNav,
+  SubNavHeader,
+  SubNavSections,
+  SubNavSection,
+  SubNavLinkSection,
+  Box,
+  TextButton,
+  Accordion,
+} from '@strapi/design-system';
 import { Plus } from '@strapi/icons';
 import upperFirst from 'lodash/upperFirst';
 import { useIntl } from 'react-intl';
@@ -20,86 +30,157 @@ const SubNavLinkCustom = styled(SubNavLink)`
     }
   }
 `;
-const ContentTypeBuilderNav = ()=>{
-    const { menu, search } = useContentTypeBuilderMenu();
-    const { formatMessage } = useIntl();
-    const pluginName = formatMessage({
-        id: getTrad('plugin.name'),
-        defaultMessage: 'Content-Type Builder'
-    });
-    return /*#__PURE__*/ jsxs(SubNav, {
-        "aria-label": pluginName,
-        children: [
-            /*#__PURE__*/ jsx(SubNavHeader, {
-                searchable: true,
-                value: search.value,
-                onClear: ()=>search.clear(),
-                onChange: (e)=>search.onChange(e.target.value),
-                label: pluginName,
-                searchLabel: formatMessage({
-                    id: 'global.search',
-                    defaultMessage: 'Search'
-                })
-            }),
-            /*#__PURE__*/ jsx(SubNavSections, {
-                children: menu.map((section)=>/*#__PURE__*/ jsxs(Fragment, {
-                        children: [
-                            /*#__PURE__*/ jsx(SubNavSection, {
-                                label: formatMessage({
-                                    id: section.title.id,
-                                    defaultMessage: section.title.defaultMessage
+
+/* ---------- helpers for grouping ---------- */
+const groupByFirstWord = (links) =>
+  links.reduce((acc, link) => {
+    const first = upperFirst(link.title).split(' ')[0];
+    if (!acc[first]) acc[first] = [];
+    acc[first].push(link);
+    return acc;
+  }, {});
+
+const splitGroups = (links) => {
+  const grouped = groupByFirstWord(links);
+  const accordionGroups = [];
+  const singles = [];
+
+  Object.entries(grouped).forEach(([first, arr]) => {
+    if (arr.length > 1) accordionGroups.push([first, arr]);
+    else singles.push(arr[0]);
+  });
+
+  return { accordionGroups, singles };
+};
+/* ----------------------------------------- */
+
+const ContentTypeBuilderNav = () => {
+  const { menu, search } = useContentTypeBuilderMenu();
+  const { formatMessage } = useIntl();
+
+  const pluginName = formatMessage({
+    id: getTrad('plugin.name'),
+    defaultMessage: 'Content-Type Builder',
+  });
+
+  return /*#__PURE__*/ jsxs(SubNav, {
+    'aria-label': pluginName,
+    children: [
+      /*#__PURE__*/ jsx(SubNavHeader, {
+        searchable: true,
+        value: search.value,
+        onClear: () => search.clear(),
+        onChange: (e) => search.onChange(e.target.value),
+        label: pluginName,
+        searchLabel: formatMessage({
+          id: 'global.search',
+          defaultMessage: 'Search',
+        }),
+      }),
+      /*#__PURE__*/ jsx(SubNavSections, {
+        children: menu.map((section) =>
+          /*#__PURE__*/ jsxs(Fragment, {
+            children: [
+              /*#__PURE__*/ jsx(SubNavSection, {
+                label: formatMessage({
+                  id: section.title.id,
+                  defaultMessage: section.title.defaultMessage,
+                }),
+                collapsable: true,
+                badgeLabel: section.linksCount.toString(),
+                children: (() => {
+                  /* rozlišíme jednoduché vs. nested linky */
+                  const simple = [];
+                  const nested = [];
+                  section.links.forEach((l) => (l.links ? nested.push(l) : simple.push(l)));
+
+                  const { accordionGroups, singles } = splitGroups(simple);
+
+                  return /*#__PURE__*/ jsxs(Fragment, {
+                    children: [
+                      accordionGroups.length > 0 &&
+                        /*#__PURE__*/ jsx(Accordion.Root, {
+                          children: accordionGroups.map(([group, links]) =>
+                            /*#__PURE__*/ jsxs(Accordion.Item, {
+                              value: `grp-${group}`,
+                              children: [
+                                /*#__PURE__*/ jsx(Accordion.Header, {
+                                  children: /*#__PURE__*/ jsx(Accordion.Trigger, {
+                                    children: upperFirst(group),
+                                  }),
                                 }),
-                                collapsable: true,
-                                badgeLabel: section.linksCount.toString(),
-                                children: section.links.map((link)=>{
-                                    if (link.links) {
-                                        return /*#__PURE__*/ jsx(SubNavLinkSection, {
-                                            label: upperFirst(link.title),
-                                            children: link.links.map((subLink)=>/*#__PURE__*/ jsx(SubNavLink, {
-                                                    tag: NavLink,
-                                                    to: subLink.to,
-                                                    active: subLink.active,
-                                                    isSubSectionChild: true,
-                                                    children: upperFirst(formatMessage({
-                                                        id: subLink.name,
-                                                        defaultMessage: subLink.title
-                                                    }))
-                                                }, subLink.name))
-                                        }, link.name);
-                                    }
-                                    return /*#__PURE__*/ jsx(SubNavLinkCustom, {
-                                        tag: NavLink,
-                                        to: link.to,
-                                        active: link.active,
-                                        width: "100%",
-                                        children: upperFirst(formatMessage({
-                                            id: link.name,
-                                            defaultMessage: link.title
-                                        }))
-                                    }, link.name);
-                                })
-                            }),
-                            section.customLink && /*#__PURE__*/ jsx(Box, {
-                                paddingLeft: 7,
-                                children: /*#__PURE__*/ jsx(TextButton, {
-                                    onClick: section.customLink.onClick,
-                                    startIcon: /*#__PURE__*/ jsx(Plus, {
-                                        width: "0.8rem",
-                                        height: "0.8rem"
-                                    }),
-                                    marginTop: 2,
-                                    cursor: "pointer",
-                                    children: formatMessage({
-                                        id: section.customLink.id,
-                                        defaultMessage: section.customLink.defaultMessage
-                                    })
-                                })
-                            })
-                        ]
-                    }, section.name))
-            })
-        ]
-    });
+                                /*#__PURE__*/ jsx(Accordion.Content, {
+                                  children: links.map((lnk) =>
+                                    /*#__PURE__*/ jsx(SubNavLinkCustom, {
+                                      tag: NavLink,
+                                      to: lnk.to,
+                                      active: lnk.active,
+                                      isSubSectionChild: true,
+                                      children: upperFirst(
+                                        formatMessage({ id: lnk.name, defaultMessage: lnk.title }),
+                                      ),
+                                    }, lnk.name),
+                                  ),
+                                }),
+                              ],
+                            }, group),
+                          ),
+                        }),
+
+                      /* vnořené subsekce zůstávají beze změny */
+                      nested.map((link) =>
+                        /*#__PURE__*/ jsx(SubNavLinkSection, {
+                          label: upperFirst(link.title),
+                          children: link.links.map((sub) =>
+                            /*#__PURE__*/ jsx(SubNavLink, {
+                              tag: NavLink,
+                              to: sub.to,
+                              active: sub.active,
+                              isSubSectionChild: true,
+                              children: upperFirst(
+                                formatMessage({ id: sub.name, defaultMessage: sub.title }),
+                              ),
+                            }, sub.name),
+                          ),
+                        }, link.name),
+                      ),
+
+                      /* singles pod akordeony */
+                      singles.map((lnk) =>
+                        /*#__PURE__*/ jsx(SubNavLinkCustom, {
+                          tag: NavLink,
+                          to: lnk.to,
+                          active: lnk.active,
+                          width: '100%',
+                          children: upperFirst(
+                            formatMessage({ id: lnk.name, defaultMessage: lnk.title }),
+                          ),
+                        }, lnk.name),
+                      ),
+                    ],
+                  });
+                })(),
+              }),
+              section.customLink &&
+                /*#__PURE__*/ jsx(Box, {
+                  paddingLeft: 7,
+                  children: /*#__PURE__*/ jsx(TextButton, {
+                    onClick: section.customLink.onClick,
+                    startIcon: /*#__PURE__*/ jsx(Plus, { width: '0.8rem', height: '0.8rem' }),
+                    marginTop: 2,
+                    cursor: 'pointer',
+                    children: formatMessage({
+                      id: section.customLink.id,
+                      defaultMessage: section.customLink.defaultMessage,
+                    }),
+                  }),
+                }),
+            ],
+          }, section.name),
+        ),
+      }),
+    ],
+  });
 };
 
 export { ContentTypeBuilderNav };
