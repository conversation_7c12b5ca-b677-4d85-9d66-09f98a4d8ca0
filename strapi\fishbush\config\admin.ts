export default ({ env }) => {
  const authSecret = env('ADMIN_JWT_SECRET') || env('STRAPI_ADMIN_JWT_SECRET');
  const apiToken = env('API_TOKEN_SALT') || env('STRAPI_API_TOKEN_SALT');
  const transferToken = env('TRANSFER_TOKEN_SALT') || env('STRAPI_TRANSFER_TOKEN_SALT');

  return {
    auth: {
      secret: authSecret,
    },
    apiToken: {
      salt: apiToken,
    },
    transfer: {
      token: {
        salt: transferToken,
      },
    },
    auditLogs: {
      enabled: false,
    },
    flags: {
      nps: env.bool('FLAG_NPS', false),
      promoteEE: env.bool('FLAG_PROMOTE_EE', false),
    },
  }

};
