<script setup lang="ts">
import { computed } from 'vue'
import Default from './Sections/Default.vue'
import FullBarColor from './Sections/FullBarColor.vue'

/* ----------------- Types ----------------- */
type Sections = 'default' | 'fullBarColor'

interface DefaultConfig {
  title: string
}

interface FullBarColorConfig {
  title: string
  color: string
}

type SectionConfigMap = {
  default: DefaultConfig
  fullBarColor: FullBarColorConfig
}

interface SectionProps<T extends Sections = Sections> {
  id: string
  type: T
  sectionConfig: SectionConfigMap[T]
}

/* ----------------- Props ----------------- */
const props = defineProps<SectionProps>()

/* ----------------- Components Map ----------------- */
const componentMap = {
  default: Default,
  fullBarColor: FullBarColor,
} satisfies Record<Sections, any>

/* ----------------- Computed ----------------- */
const CurrentComponent = computed(() => componentMap[props.type])
</script>

<template>
  <div
    class="flex flex-col gap-4 h-full w-full items-center"
    :id="props.id"
  >
    <component
      :is="CurrentComponent"
      :options="props.sectionConfig"
    >
      <slot />
    </component>
  </div>
</template>
