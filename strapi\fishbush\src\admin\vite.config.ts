import { mergeConfig, type UserConfig } from 'vite';

export default (config: UserConfig) => {
  // Important: always return the modified config
  const allowedHosts = ['localhost', '127.0.0.1', 'odevzdavani.tourdeapp.cz', 'fishbush.scg.cz', 'strapi.scg.zalubo.com'];

  return mergeConfig(config, {
    resolve: {
      alias: {
        '@': '/src',
      },
    },
    server: {
      allowedHosts
    }
  });
};
