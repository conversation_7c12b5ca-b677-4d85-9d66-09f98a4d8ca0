diff --git a/node_modules/@strapi/content-manager/dist/admin/components/LeftMenu.mjs b/node_modules/@strapi/content-manager/dist/admin/components/LeftMenu.mjs
index 99a6347..bd6ed56 100644
--- a/node_modules/@strapi/content-manager/dist/admin/components/LeftMenu.mjs
+++ b/node_modules/@strapi/content-manager/dist/admin/components/LeftMenu.mjs
@@ -1,7 +1,7 @@
 import { jsxs, jsx } from 'react/jsx-runtime';
 import * as React from 'react';
 import { useQueryParams } from '@strapi/admin/strapi-admin';
-import { SubNavLink, useFilter, useCollator, SubNav, SubNavHeader, SubNavSections, SubNavSection } from '@strapi/design-system';
+import { SubNavLink, useFilter, useCollator, SubNav, SubNavHeader, SubNavSections, SubNavSection, Accordion } from '@strapi/design-system';
 import { stringify, parse } from 'qs';
 import { useIntl } from 'react-intl';
 import { NavLink } from 'react-router-dom';
@@ -34,6 +34,32 @@ const LeftMenu = ()=>{
     const formatter = useCollator(locale, {
         sensitivity: 'base'
     });
+
+    const groupByFirstWord = (links) => {
+    return links.reduce((acc, link) => {
+        const first = link.title.split(' ')[0];    // první slovo
+        if (!acc[first]) acc[first] = [];
+        acc[first].push(link);
+        return acc;
+    }, {});
+    };
+
+    const splitGroups = (links) => {
+    const grouped = groupByFirstWord(links);
+    const accordionGroups = [];
+    const singles = [];
+
+    Object.entries(grouped).forEach(([first, arr]) => {
+        if (arr.length > 1) {
+        accordionGroups.push([first, arr]);
+        } else {
+        singles.push(arr[0]);         // jediný link ve skupině
+        }
+    });
+
+    return { accordionGroups, singles };
+    };
+
     const menu = React.useMemo(()=>[
             {
                 id: 'collectionTypes',
@@ -123,24 +149,65 @@ const LeftMenu = ()=>{
             }),
             /*#__PURE__*/ jsx(SubNavSections, {
                 children: menu.map((section)=>{
-                    return /*#__PURE__*/ jsx(SubNavSection, {
-                        label: section.title,
-                        badgeLabel: section.links.length.toString(),
-                        children: section.links.map((link)=>{
-                            return /*#__PURE__*/ jsx(SubNavLinkCustom, {
-                                tag: NavLink,
-                                to: {
-                                    pathname: link.to,
-                                    search: stringify({
-                                        ...parse(link.search ?? ''),
-                                        plugins: getPluginsParamsForLink(link)
-                                    })
-                                },
-                                width: "100%",
-                                children: link.title
-                            }, link.uid);
-                        })
-                    }, section.id);
+return /*#__PURE__*/ jsx(SubNavSection, {
+  label: section.title,
+  badgeLabel: section.links.length.toString(),
+  children: (() => {
+    const { accordionGroups, singles } = splitGroups(section.links);
+
+    return /*#__PURE__*/ jsxs(React.Fragment, {
+      children: [
+        accordionGroups.length > 0 &&
+          /*#__PURE__*/ jsx(Accordion.Root, {
+            children: accordionGroups.map(([group, links]) =>
+              /*#__PURE__*/ jsxs(Accordion.Item, {
+                value: `grp-${group}`,
+                children: [
+                  /*#__PURE__*/ jsx(Accordion.Header, {
+                    children: /*#__PURE__*/ jsx(Accordion.Trigger, { children: group })
+                  }),
+                  /*#__PURE__*/ jsx(Accordion.Content, {
+                    children: links.map((link) =>
+                      /*#__PURE__*/ jsx(SubNavLinkCustom, {
+                        tag: NavLink,
+                        to: {
+                          pathname: link.to,
+                          search: stringify({
+                            ...parse(link.search ?? ''),
+                            plugins: getPluginsParamsForLink(link)
+                          })
+                        },
+                        width: "100%",
+                        children: link.title
+                      }, link.uid)
+                    )
+                  })
+                ]
+              }, group)
+            )
+          }),
+
+        /* odkazy, které neměly ‚sourozence‘ */
+        singles.map((link) =>
+          /*#__PURE__*/ jsx(SubNavLinkCustom, {
+            tag: NavLink,
+            to: {
+              pathname: link.to,
+              search: stringify({
+                ...parse(link.search ?? ''),
+                plugins: getPluginsParamsForLink(link)
+              })
+            },
+            width: "100%",
+            children: link.title
+          }, link.uid)
+        )
+      ]
+    });
+  })()
+}, section.id);
+
+
                 })
             })
         ]
