import axios from "axios";
import qs from "qs";

interface IStrapiResponse {
	data: {
		data: any[];
		meta: {
			pagination: {
				total: number;
			};
		};
	};
}

async function getWebContent(slug: string) {
	const qsSlug = qs.stringify(
		{
			filters: {
				slug: {
					$eq: slug,
				},
			},
			populate: {
				content: {
					populate: "*",
				},
			},
		},
		{
			encodeValuesOnly: true,
		},
	);
	const r: IStrapiResponse = await axios.get(
		`${Bun.env.CMS_URL}/api/web-contents?${qsSlug}`,
		{
			headers: {
				Authorization: `Bearer ${Bun.env.CMS_KEY}`,
			},
		},
	);

	if (r.data.meta.pagination.total === 0) {
		return {
			error: "Not Found",
		};
	}

	return r.data.data[0];
}

export { getWebContent };
