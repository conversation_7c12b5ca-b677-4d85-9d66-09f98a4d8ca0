<script setup lang="ts">
interface DefaultOptions {
  title: string
}

const props = defineProps<{
  options: DefaultOptions
}>()
</script>

<template>
  <div class="flex flex-col gap-4 w-3/5">
    <h2
      class="w-fit pl-4 pr-12 pt-2 pb-0.5 text-2xl text-black border-b-2 border-black/20"
    >
      {{ props.options.title }}
    </h2>

    <div class="flex flex-wrap gap-4">
      <slot />
    </div>
  </div>
</template>
