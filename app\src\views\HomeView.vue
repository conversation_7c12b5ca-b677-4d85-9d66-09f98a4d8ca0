<script setup lang="ts">
import SkillBox from '@/components/SkillBox.vue'
import Section from '@/components/Section.vue'

import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
gsap.registerPlugin(ScrollTrigger)

import { useI18n } from 'vue-i18n'
import { onMounted, ref } from 'vue'
const { t } = useI18n()


const currentHost = window.location.host
</script>

<template>
  <!-- page wrapper: full viewport, now no page-scroll -->
  <div class="w-full min-w-64 h-screen bg-neutral-900 px-8 pb-8 flex flex-col
           overflow-hidden"><!-- ← NEW -->

    <!-- top bar -->
    <div class="bg-neutral-900 my-1 h-10 w-full flex items-center">
      <div class="text-white flex gap-2 flex-1">
        <!-- traffic-light buttons -->
        <div class="bg-red-500 rounded-full aspect-square p-0.5">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
            stroke="currentColor" class="size-4">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
          </svg>
        </div>
        <div class="bg-yellow-500 rounded-full aspect-square p-0.5">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
            stroke="currentColor" class="size-4">
            <path stroke-linecap="round" stroke-linejoin="round"
              d="M16.5 8.25V6a2.25 2.25 0 0 0-2.25-2.25H6A2.25 2.25 0 0 0 3.75 6v8.25A2.25 2.25 0 0 0 6 16.5h2.25m8.25-8.25H18a2.25 2.25 0 0 1 2.25 2.25V18A2.25 2.25 0 0 1 18 20.25h-7.5A2.25 2.25 0 0 1 8.25 18v-1.5m8.25-8.25h-6a2.25 2.25 0 0 0-2.25 2.25v6" />
          </svg>
        </div>
        <div class="bg-green-500 rounded-full aspect-square p-0.5">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
            stroke="currentColor" class="size-4">
            <path stroke-linecap="round" stroke-linejoin="round" d="M5 12h14" />
          </svg>
        </div>
      </div>
      <div class="w-96 rounded-lg border-2 border-white/40 flex justify-center items-center
               text-white/50 text-sm py-1 flex-1">
        {{ currentHost }}
      </div>
      <div class="flex-1" />
    </div>

    <!-- center column -->
    <div class="flex-1 flex justify-center min-h-0"><!-- ← NEW -->
      <!-- white card with its own scrollbar -->
      <div class="bg-white/90 rounded-lg flex flex-col w-full flex-1 gap-24
               overflow-y-auto">

        <!-- hero -->
        <div class="flex flex-col gap-4 min-h-screen w-full items-center justify-center
                 bg-gradient-to-bl from-amber-900/40 to-amber-600/50">
          <div class="flex flex-col w-fit">
            <div class="flex flex-col w-fit">
              <div class="self-end text-neutral-100 text-lg">I am</div>
              <div class="bg-amber-500 text-white pr-4 pl-12 py-2 text-2xl w-fit">
                Vojta
              </div>
            </div>
            <div class="bg-amber-600 text-white ml-12 pl-4 pr-12 py-2 text-2xl">
              UX designer <br /> &amp; Fullstack developer
            </div>
          </div>

          <div class="w-2/5 text-white">
            Lorem ipsum dolor sit amet consectetur adipisicing elit. Maiores officiis
            nostrum voluptatum maxime voluptate! Qui ipsam placeat aspernatur iusto
            ex illum, deserunt, laudantium sequi optio vitae pariatur et sit modi?
          </div>

          <a href="#experiences" class="px-8 py-2 mt-8 cursor-pointer rounded-lg shadow-sm bg-amber-500
                    text-lg text-white">
            Get To Know More
          </a>
        </div>

        <!-- experiences -->
        <Section type="default" :section-config="{ title: 'My Experiences'}" id="experiences">
          <SkillBox>
            <template #title>Web Development</template>
            <template #description>
              Lorem ipsum dolor sit amet consectetur adipisicing elit. Sint, nesciunt quod.
              Nam nisi libero voluptates maxime magnam modi corporis pariatur,
              obcaecati minus culpa a quis, aliquam consectetur nemo voluptatibus qui!
            </template>
          </SkillBox>

          <SkillBox>
            <template #title>DevOps</template>
            <template #description>
              My main focus is on web development. I have experience with VueJS, React,
              and NextJS. I am also familiar with TypeScript and JavaScript.
            </template>
          </SkillBox>

          <SkillBox>
            <template #title>UX design</template>
            <template #description>
              My main focus is on web development. I have experience with VueJS, React,
              and NextJS. I am also familiar with TypeScript and JavaScript.
            </template>
          </SkillBox>
        </Section>

        <!-- projects -->
        <Section type="fullBarColor"
          :section-config="{title: 'My Projects', color: 'bg-amber-500 text-white' }"
          id="projects">
          <div class="flex w-full">
            <div class="flex-1">
              Hello World
            </div>
            <div class="flex-1">
              Hello World
            </div>
          </div>
        </Section>
      </div>
    </div>
  </div>
</template>
