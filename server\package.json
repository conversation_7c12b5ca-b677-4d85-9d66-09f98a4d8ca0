{"name": "app", "version": "1.0.50", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "bun run --watch src/index.ts"}, "dependencies": {"@types/qs": "^6.14.0", "axios": "^1.10.0", "elysia": "latest", "qs": "^6.14.0"}, "devDependencies": {"bun-types": "latest", "@biomejs/biome": "^2.1.2"}, "module": "src/index.js", "type": "module", "peerDependencies": {"typescript": "^5.0.0"}}