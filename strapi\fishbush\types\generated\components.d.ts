import type { Schema, Struct } from '@strapi/strapi';

export interface ContentBlocksArticleList extends Struct.ComponentSchema {
  collectionName: 'components_content_blocks_article_lists';
  info: {
    displayName: 'ArticleList';
    icon: 'server';
  };
  attributes: {
    categories: Schema.Attribute.Relation<
      'oneToMany',
      'api::web-article-category.web-article-category'
    >;
    content: Schema.Attribute.RichText;
    publishDate: Schema.Attribute.DateTime & Schema.Attribute.Required;
    unpublishDate: Schema.Attribute.DateTime;
  };
}

export interface ContentBlocksBasicContent extends Struct.ComponentSchema {
  collectionName: 'components_content_blocks_basic_contents';
  info: {
    description: '';
    displayName: 'BasicContent';
  };
  attributes: {
    content: Schema.Attribute.RichText;
    publishDate: Schema.Attribute.DateTime &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'1999-12-31T23:00:00.000Z'>;
    unpublishDate: Schema.Attribute.DateTime;
  };
}

export interface ContentBlocksContacts extends Struct.ComponentSchema {
  collectionName: 'components_content_blocks_contacts';
  info: {
    displayName: 'Contacts';
    icon: 'user';
  };
  attributes: {
    content: Schema.Attribute.RichText;
    publishDate: Schema.Attribute.DateTime & Schema.Attribute.Required;
    unpublishDate: Schema.Attribute.DateTime;
  };
}

export interface ContentBlocksHomeSplashScreen extends Struct.ComponentSchema {
  collectionName: 'components_content_blocks_home_splash_screens';
  info: {
    description: '';
    displayName: 'HomeSplashScreen';
    icon: 'grid';
  };
  attributes: {
    content: Schema.Attribute.RichText;
    picture: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    publishDate: Schema.Attribute.DateTime & Schema.Attribute.Required;
    unpublishDate: Schema.Attribute.DateTime;
  };
}

export interface ContentBlocksPartners extends Struct.ComponentSchema {
  collectionName: 'components_content_blocks_partners';
  info: {
    description: '';
    displayName: 'Partners';
    icon: 'handHeart';
  };
  attributes: {
    content: Schema.Attribute.RichText;
    publishDate: Schema.Attribute.DateTime & Schema.Attribute.Required;
    unpublishDate: Schema.Attribute.DateTime;
  };
}

export interface ContentBlocksRegistrationForm extends Struct.ComponentSchema {
  collectionName: 'components_content_blk_reg_forms';
  info: {
    description: '';
    displayName: 'RegistrationForm';
    icon: 'write';
  };
  attributes: {
    checkFields: Schema.Attribute.Component<
      'multimedia-blocks.form-check-field',
      true
    >;
    content: Schema.Attribute.RichText;
    eventPick: Schema.Attribute.Enumeration<
      ['None', 'SameRegion', 'SameTopRegion', 'Any']
    > &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'None'>;
    hasCaptain: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<true>;
    hasInvoice: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<true>;
    hasReferral: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<false>;
    hasTeacher: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<true>;
    maxContestants: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    minContestants: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      > &
      Schema.Attribute.DefaultTo<1>;
    publishDate: Schema.Attribute.DateTime & Schema.Attribute.Required;
    registrationSuccessUrl: Schema.Attribute.String;
    schoolTypes: Schema.Attribute.Relation<
      'oneToMany',
      'api::core-school-type.core-school-type'
    >;
    substituteContestants: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      > &
      Schema.Attribute.DefaultTo<0>;
    unpublishDate: Schema.Attribute.DateTime;
  };
}

export interface ContentBlocksRegistrationSuccess
  extends Struct.ComponentSchema {
  collectionName: 'components_content_blocks_registration_successes';
  info: {
    description: '';
    displayName: 'RegistrationSuccess';
    icon: 'check';
  };
  attributes: {
    content: Schema.Attribute.RichText;
    publishDate: Schema.Attribute.DateTime &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'1999-12-31T23:00:00.000Z'>;
    refNextUrl: Schema.Attribute.String &
      Schema.Attribute.DefaultTo<'/registrace-tymu'>;
    unpublishDate: Schema.Attribute.DateTime;
  };
}

export interface ContentBlocksTimedContent extends Struct.ComponentSchema {
  collectionName: 'components_content_blocks_timed_contents';
  info: {
    description: '';
    displayName: 'TimedContent';
    icon: 'clock';
  };
  attributes: {
    content: Schema.Attribute.Component<'content-blocks.basic-content', true>;
    publishDate: Schema.Attribute.DateTime & Schema.Attribute.Required;
    unpublishDate: Schema.Attribute.DateTime;
  };
}

export interface DiscordDiscordIdList extends Struct.ComponentSchema {
  collectionName: 'components_discord_discord_id_lists';
  info: {
    displayName: 'Discord ID list';
    icon: 'cube';
  };
  attributes: {
    discordId: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
  };
}

export interface MailingFullEMail extends Struct.ComponentSchema {
  collectionName: 'components_mailing_full_e_mails';
  info: {
    description: '';
    displayName: 'School E-mail List';
    icon: 'exit';
  };
  attributes: {
    contact: Schema.Attribute.Relation<
      'oneToOne',
      'api::core-school-contact.core-school-contact'
    >;
    firstName: Schema.Attribute.String;
    lastName: Schema.Attribute.String;
    mail: Schema.Attribute.Email & Schema.Attribute.Unique;
  };
}

export interface MailingListOfSentEMails extends Struct.ComponentSchema {
  collectionName: 'components_mailing_list_of_sent_e_mails';
  info: {
    description: '';
    displayName: 'List of sent e-mails';
    icon: 'write';
  };
  attributes: {
    content: Schema.Attribute.Text & Schema.Attribute.Required;
    fromMail: Schema.Attribute.String & Schema.Attribute.Required;
    fromName: Schema.Attribute.String;
    recipient: Schema.Attribute.Relation<
      'oneToOne',
      'api::core-school-contact.core-school-contact'
    >;
  };
}

export interface MultimediaBlocksCroppedImage extends Struct.ComponentSchema {
  collectionName: 'components_multimedia_blk_crop_img';
  info: {
    description: '';
    displayName: 'CroppedImage';
    icon: 'picture';
  };
  attributes: {
    height: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      >;
    image: Schema.Attribute.Media<'images'> & Schema.Attribute.Required;
    projectType: Schema.Attribute.Relation<
      'oneToOne',
      'api::core-project-type.core-project-type'
    >;
    width: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      >;
    x: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      >;
    y: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      >;
  };
}

export interface MultimediaBlocksFormCheckField extends Struct.ComponentSchema {
  collectionName: 'components_multimedia_blocks_form_check_fields';
  info: {
    description: '';
    displayName: 'FormCheckField';
    icon: 'question';
  };
  attributes: {
    content: Schema.Attribute.RichText & Schema.Attribute.Required;
    isRequired: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<true>;
    name: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface TdaCtfComponentsEmail extends Struct.ComponentSchema {
  collectionName: 'components_tda_ctf_components_emails';
  info: {
    displayName: 'email';
  };
  attributes: {
    content: Schema.Attribute.RichText;
    fromEmail: Schema.Attribute.String;
    fromName: Schema.Attribute.String;
    sentAt: Schema.Attribute.DateTime;
    subject: Schema.Attribute.String;
  };
}

export interface TdaCtfComponentsGenericNote extends Struct.ComponentSchema {
  collectionName: 'components_tda_ctf_components_generic_notes';
  info: {
    displayName: 'genericNote';
  };
  attributes: {
    content: Schema.Attribute.RichText;
  };
}

export interface TdaCtfComponentsStickyNote extends Struct.ComponentSchema {
  collectionName: 'components_tda_ctf_components_sticky_notes';
  info: {
    displayName: 'stickyNote';
  };
  attributes: {
    content: Schema.Attribute.Text &
      Schema.Attribute.SetMinMaxLength<{
        maxLength: 255;
      }>;
    header: Schema.Attribute.String;
  };
}

export interface TdaFisherPhasesInfo extends Struct.ComponentSchema {
  collectionName: 'components_tda_fisher_phases_infos';
  info: {
    description: '';
    displayName: 'info';
    icon: 'information';
  };
  attributes: {
    content: Schema.Attribute.RichText;
    links: Schema.Attribute.Component<'tda-fisher-phases.link', true>;
    title: Schema.Attribute.String;
  };
}

export interface TdaFisherPhasesInfoPanel extends Struct.ComponentSchema {
  collectionName: 'components_tda_fisher_phases_info_panels';
  info: {
    description: '';
    displayName: 'info-panel';
  };
  attributes: {
    content: Schema.Attribute.RichText & Schema.Attribute.Required;
    header: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface TdaFisherPhasesLink extends Struct.ComponentSchema {
  collectionName: 'components_tda_fisher_phases_links';
  info: {
    displayName: 'link';
  };
  attributes: {
    name: Schema.Attribute.String & Schema.Attribute.Required;
    url: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface TdaFisherPhasesResult extends Struct.ComponentSchema {
  collectionName: 'components_tda_fisher_phases_results';
  info: {
    description: '';
    displayName: 'result';
    icon: 'bulletList';
  };
  attributes: {
    file: Schema.Attribute.Media<'files'>;
    tda_team: Schema.Attribute.Relation<
      'oneToOne',
      'api::tda-core-team.tda-core-team'
    >;
  };
}

export interface TdaRezfishMeetComponentsAvailableTime
  extends Struct.ComponentSchema {
  collectionName: 'components_tda_meet_components_available_times';
  info: {
    displayName: 'AvailableTime';
  };
  attributes: {
    friday: Schema.Attribute.Component<
      'tda-rezfish-meet-components.time-block',
      true
    >;
    monday: Schema.Attribute.Component<
      'tda-rezfish-meet-components.time-block',
      true
    >;
    saturday: Schema.Attribute.Component<
      'tda-rezfish-meet-components.time-block',
      true
    >;
    sunday: Schema.Attribute.Component<
      'tda-rezfish-meet-components.time-block',
      true
    >;
    thursday: Schema.Attribute.Component<
      'tda-rezfish-meet-components.time-block',
      true
    >;
    tuesday: Schema.Attribute.Component<
      'tda-rezfish-meet-components.time-block',
      true
    >;
    wednesday: Schema.Attribute.Component<
      'tda-rezfish-meet-components.time-block',
      true
    >;
  };
}

export interface TdaRezfishMeetComponentsTimeBlock
  extends Struct.ComponentSchema {
  collectionName: 'components_tda_meet_components_time_blocks';
  info: {
    displayName: 'TimeBlock';
  };
  attributes: {
    end: Schema.Attribute.Time;
    start: Schema.Attribute.Time;
  };
}

export interface TdcDeployment extends Struct.ComponentSchema {
  collectionName: 'components_tdc_deployments';
  info: {
    description: '';
    displayName: 'Deployment';
    icon: 'database';
  };
  attributes: {
    branch: Schema.Attribute.String;
    commit: Schema.Attribute.String;
    config: Schema.Attribute.Text;
  };
}

declare module '@strapi/strapi' {
  export module Public {
    export interface ComponentSchemas {
      'content-blocks.article-list': ContentBlocksArticleList;
      'content-blocks.basic-content': ContentBlocksBasicContent;
      'content-blocks.contacts': ContentBlocksContacts;
      'content-blocks.home-splash-screen': ContentBlocksHomeSplashScreen;
      'content-blocks.partners': ContentBlocksPartners;
      'content-blocks.registration-form': ContentBlocksRegistrationForm;
      'content-blocks.registration-success': ContentBlocksRegistrationSuccess;
      'content-blocks.timed-content': ContentBlocksTimedContent;
      'discord.discord-id-list': DiscordDiscordIdList;
      'mailing.full-e-mail': MailingFullEMail;
      'mailing.list-of-sent-e-mails': MailingListOfSentEMails;
      'multimedia-blocks.cropped-image': MultimediaBlocksCroppedImage;
      'multimedia-blocks.form-check-field': MultimediaBlocksFormCheckField;
      'tda-ctf-components.email': TdaCtfComponentsEmail;
      'tda-ctf-components.generic-note': TdaCtfComponentsGenericNote;
      'tda-ctf-components.sticky-note': TdaCtfComponentsStickyNote;
      'tda-fisher-phases.info': TdaFisherPhasesInfo;
      'tda-fisher-phases.info-panel': TdaFisherPhasesInfoPanel;
      'tda-fisher-phases.link': TdaFisherPhasesLink;
      'tda-fisher-phases.result': TdaFisherPhasesResult;
      'tda-rezfish-meet-components.available-time': TdaRezfishMeetComponentsAvailableTime;
      'tda-rezfish-meet-components.time-block': TdaRezfishMeetComponentsTimeBlock;
      'tdc.deployment': TdcDeployment;
    }
  }
}
