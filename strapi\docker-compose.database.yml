version: "3.9"

services:
  postgres:
    container_name: fishbush_db_dev
    image: postgres:14
    restart: unless-stopped
    env_file: fishbush/.env
    environment:
      POSTGRES_USER: ${DATABASE_USERNAME}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
      POSTGRES_DB: ${DATABASE_NAME}
    ports:
      - "5432:5432"
    volumes:
      - strapi-db-data-dev:/var/lib/postgresql/data
    networks:
      - fishbush_network_dev

volumes:
  strapi-db-data-dev:

networks:
  fishbush_network_dev:
    driver: bridge
