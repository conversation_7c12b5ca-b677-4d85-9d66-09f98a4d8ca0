@import 'tailwindcss';     /* pulls in core framework  */
@import "tailwindcss-intersect";

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@layer components {
  * {
    @apply scroll-smooth scroll-m-8;
  }
  code {
    @apply bg-neutral-950/30 px-2 rounded-sm;
  }
}