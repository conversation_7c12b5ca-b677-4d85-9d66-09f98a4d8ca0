<script setup lang="ts">
import type { PropType } from 'vue';

interface IOptions {
    title: string
    color: string
}

defineProps({
    options: {
        type: Object as PropType<IOptions>,
        required: true
    }
})
</script>


<template>
    <div :class="[options.color, 'flex flex-col items-center py-2 gap-4 w-full py-8']">
        <div class="w-3/5 flex flex-col gap-4">
            <div>
                <h2 class="text-2xl pt-2 pb-0.5 w-fit">
                    {{ options.title }}
                </h2>
            </div>
            <div class="flex flex-wrap gap-4">
                <slot></slot>
            </div>
        </div>
    </div>
</template>