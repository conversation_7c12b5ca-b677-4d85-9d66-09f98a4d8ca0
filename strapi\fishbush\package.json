{"name": "fishbush", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"env-build": "env-cmd strapi build", "env-develop": "env-cmd strapi develop", "postinstall": "patch-package", "build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi ts:generate-types && strapi develop", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry", "strapi:generate-types": "strapi ts:generate-types"}, "dependencies": {"@strapi/plugin-graphql": "^5.14.0", "@strapi/plugin-users-permissions": "5.12.4", "@strapi/strapi": "5.12.4", "patch-package": "^8.0.0", "pg": "8.8.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "strapi-plugin-sso": "1.0.2", "styled-components": "^6.0.0"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "env-cmd": "^10.1.0", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "telemetryDisabled": true}}