export default ({ env }: { env: (key: string, defaultValue?: any) => any }) => ({
  'strapi-plugin-sso': {
    enabled: true,
    config: {
      REMEMBER_ME: true,
      // Google OAuth Configuration
      GOOGLE_OAUTH_CLIENT_ID: env('GOOGLE_OAUTH_CLIENT_ID', '[Client ID created in GCP]'),
      GOOGLE_OAUTH_CLIENT_SECRET: env('GOOGLE_OAUTH_CLIENT_SECRET', '[Client Secret created in GCP]'),
      GOOGLE_OAUTH_REDIRECT_URI: `${env('PUBLIC_URL', 'http://localhost:1337')}/strapi-plugin-sso/google/callback`,
      GOOGLE_ALIAS: '',
      GOOGLE_GSUITE_HD: env('GOOGLE_GSUITE_HD', 'scg.cz'),
      USE_WHITELIST: false,
    }
  }
});
