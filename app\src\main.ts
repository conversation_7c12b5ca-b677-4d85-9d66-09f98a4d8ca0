import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'
import { createI18n } from 'vue-i18n'
import { Observer } from 'tailwindcss-intersect'

const i18n = createI18n({
  locale: 'en',
  messages: {
    en: {
      home: {
        title: 'Hi, I am Vojta Tmej',
        description: 'I am a software developer from Czechia. I am passionate about building clean and maintainable code. I am always looking for new challenges and opportunities to learn and grow.'
      }
    },
    cs: {
      home: {
        title: 'Aho<PERSON>, jmenuji se Vojta Tmej',
        description: 'Jsem programátor z Česka. Mám rád vytvářet čistý a udržovatelný kód. Vždycky hledám nové výzvy a možnosti ke zdokonalování.'
      }
    }
  }
})

const app = createApp(App)

app.use(createPinia())
app.use(i18n)
app.use(router)

app.mount('#app')

Observer.start()

router.afterEach(() => {
  Observer.restart()   // scan the new DOM and re-observe everything
})