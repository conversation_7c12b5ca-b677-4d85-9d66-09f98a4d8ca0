import type { Schema, Struct } from '@strapi/strapi';

export interface AdminApiToken extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_api_tokens';
  info: {
    description: '';
    displayName: 'Api Token';
    name: 'Api Token';
    pluralName: 'api-tokens';
    singularName: 'api-token';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    accessKey: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Schema.Attribute.DefaultTo<''>;
    expiresAt: Schema.Attribute.DateTime;
    lastUsedAt: Schema.Attribute.DateTime;
    lifespan: Schema.Attribute.BigInteger;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::api-token'> &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Schema.Attribute.Relation<
      'oneToMany',
      'admin::api-token-permission'
    >;
    publishedAt: Schema.Attribute.DateTime;
    type: Schema.Attribute.Enumeration<['read-only', 'full-access', 'custom']> &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'read-only'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminApiTokenPermission extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_api_token_permissions';
  info: {
    description: '';
    displayName: 'API Token Permission';
    name: 'API Token Permission';
    pluralName: 'api-token-permissions';
    singularName: 'api-token-permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'admin::api-token-permission'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    token: Schema.Attribute.Relation<'manyToOne', 'admin::api-token'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminPermission extends Struct.CollectionTypeSchema {
  collectionName: 'admin_permissions';
  info: {
    description: '';
    displayName: 'Permission';
    name: 'Permission';
    pluralName: 'permissions';
    singularName: 'permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    actionParameters: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
    conditions: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<[]>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::permission'> &
      Schema.Attribute.Private;
    properties: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
    publishedAt: Schema.Attribute.DateTime;
    role: Schema.Attribute.Relation<'manyToOne', 'admin::role'>;
    subject: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminRole extends Struct.CollectionTypeSchema {
  collectionName: 'admin_roles';
  info: {
    description: '';
    displayName: 'Role';
    name: 'Role';
    pluralName: 'roles';
    singularName: 'role';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    code: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::role'> &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Schema.Attribute.Relation<'oneToMany', 'admin::permission'>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    users: Schema.Attribute.Relation<'manyToMany', 'admin::user'>;
  };
}

export interface AdminTransferToken extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_transfer_tokens';
  info: {
    description: '';
    displayName: 'Transfer Token';
    name: 'Transfer Token';
    pluralName: 'transfer-tokens';
    singularName: 'transfer-token';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    accessKey: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Schema.Attribute.DefaultTo<''>;
    expiresAt: Schema.Attribute.DateTime;
    lastUsedAt: Schema.Attribute.DateTime;
    lifespan: Schema.Attribute.BigInteger;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'admin::transfer-token'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Schema.Attribute.Relation<
      'oneToMany',
      'admin::transfer-token-permission'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminTransferTokenPermission
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_transfer_token_permissions';
  info: {
    description: '';
    displayName: 'Transfer Token Permission';
    name: 'Transfer Token Permission';
    pluralName: 'transfer-token-permissions';
    singularName: 'transfer-token-permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'admin::transfer-token-permission'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    token: Schema.Attribute.Relation<'manyToOne', 'admin::transfer-token'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminUser extends Struct.CollectionTypeSchema {
  collectionName: 'admin_users';
  info: {
    description: '';
    displayName: 'User';
    name: 'User';
    pluralName: 'users';
    singularName: 'user';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    blocked: Schema.Attribute.Boolean &
      Schema.Attribute.Private &
      Schema.Attribute.DefaultTo<false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    email: Schema.Attribute.Email &
      Schema.Attribute.Required &
      Schema.Attribute.Private &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    firstname: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    isActive: Schema.Attribute.Boolean &
      Schema.Attribute.Private &
      Schema.Attribute.DefaultTo<false>;
    lastname: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::user'> &
      Schema.Attribute.Private;
    password: Schema.Attribute.Password &
      Schema.Attribute.Private &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    preferedLanguage: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    registrationToken: Schema.Attribute.String & Schema.Attribute.Private;
    resetPasswordToken: Schema.Attribute.String & Schema.Attribute.Private;
    roles: Schema.Attribute.Relation<'manyToMany', 'admin::role'> &
      Schema.Attribute.Private;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    username: Schema.Attribute.String;
  };
}

export interface ApiCoreContestantCoreContestant
  extends Struct.CollectionTypeSchema {
  collectionName: 'core_contestants';
  info: {
    displayName: 'CORE Contestant';
    pluralName: 'core-contestants';
    singularName: 'core-contestant';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    email: Schema.Attribute.Email & Schema.Attribute.Required;
    firstname: Schema.Attribute.String & Schema.Attribute.Required;
    gender: Schema.Attribute.Enumeration<['Man', 'Woman', 'Other']>;
    lastname: Schema.Attribute.String & Schema.Attribute.Required;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::core-contestant.core-contestant'
    > &
      Schema.Attribute.Private;
    phoneNumber: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        maxLength: 20;
      }>;
    publishedAt: Schema.Attribute.DateTime;
    role: Schema.Attribute.Enumeration<
      ['teamLeader', 'teamMember', 'teamSubstitute']
    > &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'teamMember'>;
    school: Schema.Attribute.Relation<
      'manyToOne',
      'api::core-school.core-school'
    >;
    tdaTeam: Schema.Attribute.Relation<
      'manyToOne',
      'api::tda-core-team.tda-core-team'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCoreEventGroupCoreEventGroup
  extends Struct.CollectionTypeSchema {
  collectionName: 'core_event_groups';
  info: {
    description: '';
    displayName: 'CORE Event Group';
    pluralName: 'core-event-groups';
    singularName: 'core-event-group';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    events: Schema.Attribute.Relation<
      'oneToMany',
      'api::core-event.core-event'
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::core-event-group.core-event-group'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    previousEventGroup: Schema.Attribute.Relation<
      'oneToOne',
      'api::core-event-group.core-event-group'
    >;
    project: Schema.Attribute.Relation<
      'manyToOne',
      'api::core-project.core-project'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCoreEventCoreEvent extends Struct.CollectionTypeSchema {
  collectionName: 'core_events';
  info: {
    displayName: 'CORE Event';
    pluralName: 'core-events';
    singularName: 'core-event';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    capacity: Schema.Attribute.Integer;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    eventGroup: Schema.Attribute.Relation<
      'manyToOne',
      'api::core-event-group.core-event-group'
    >;
    link: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::core-event.core-event'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String;
    place: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    school: Schema.Attribute.Relation<
      'oneToOne',
      'api::core-school.core-school'
    >;
    startAt: Schema.Attribute.DateTime & Schema.Attribute.Required;
    state: Schema.Attribute.Enumeration<
      [
        'Neotev\u0159en\u00E1',
        'Otev\u0159en\u00E1',
        'Uzav\u0159en\u00E1',
        'Zru\u0161en\u00E1',
      ]
    > &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'Otev\u0159en\u00E1'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCoreProjectTypeCoreProjectType
  extends Struct.CollectionTypeSchema {
  collectionName: 'core_project_types';
  info: {
    displayName: 'CORE Project Type';
    pluralName: 'core-project-types';
    singularName: 'core-project-type';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::core-project-type.core-project-type'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    projects: Schema.Attribute.Relation<
      'oneToMany',
      'api::core-project.core-project'
    >;
    publishedAt: Schema.Attribute.DateTime;
    tagColor: Schema.Attribute.String & Schema.Attribute.DefaultTo<'#000000'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCoreProjectCoreProject extends Struct.CollectionTypeSchema {
  collectionName: 'core_projects';
  info: {
    displayName: 'CORE Project';
    pluralName: 'core-projects';
    singularName: 'core-project';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    customName: Schema.Attribute.String;
    eventGroups: Schema.Attribute.Relation<
      'oneToMany',
      'api::core-event-group.core-event-group'
    >;
    isRunning: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<true>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::core-project.core-project'
    > &
      Schema.Attribute.Private;
    projectType: Schema.Attribute.Relation<
      'manyToOne',
      'api::core-project-type.core-project-type'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    year: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      >;
  };
}

export interface ApiCoreRegionCoreRegion extends Struct.CollectionTypeSchema {
  collectionName: 'core_regions';
  info: {
    displayName: 'CORE Region';
    pluralName: 'core-regions';
    singularName: 'core-region';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    childRegions: Schema.Attribute.Relation<
      'oneToMany',
      'api::core-region.core-region'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::core-region.core-region'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    publishedAt: Schema.Attribute.DateTime;
    schools: Schema.Attribute.Relation<
      'oneToMany',
      'api::core-school.core-school'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCoreSchoolContactCoreSchoolContact
  extends Struct.CollectionTypeSchema {
  collectionName: 'core_school_contacts';
  info: {
    displayName: 'CORE School Contact';
    pluralName: 'core-school-contacts';
    singularName: 'core-school-contact';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    email: Schema.Attribute.Email & Schema.Attribute.Required;
    firstname: Schema.Attribute.String & Schema.Attribute.Required;
    gender: Schema.Attribute.Enumeration<['Man', 'Woman', 'Other']>;
    lastname: Schema.Attribute.String & Schema.Attribute.Required;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::core-school-contact.core-school-contact'
    > &
      Schema.Attribute.Private;
    phoneNumber: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        maxLength: 20;
      }>;
    publishedAt: Schema.Attribute.DateTime;
    school: Schema.Attribute.Relation<
      'manyToOne',
      'api::core-school.core-school'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCoreSchoolTypeCoreSchoolType
  extends Struct.CollectionTypeSchema {
  collectionName: 'core_school_types';
  info: {
    description: '';
    displayName: 'CORE School Type';
    pluralName: 'core-school-types';
    singularName: 'core-school-type';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::core-school-type.core-school-type'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    publishedAt: Schema.Attribute.DateTime;
    schools: Schema.Attribute.Relation<
      'manyToMany',
      'api::core-school.core-school'
    >;
    subtypes: Schema.Attribute.Relation<
      'oneToMany',
      'api::core-school-type.core-school-type'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCoreSchoolCoreSchool extends Struct.CollectionTypeSchema {
  collectionName: 'core_schools';
  info: {
    description: '';
    displayName: 'CORE School';
    pluralName: 'core-schools';
    singularName: 'core-school';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    cin: Schema.Attribute.String;
    contactEmail: Schema.Attribute.String;
    contactPhone: Schema.Attribute.String;
    contestants: Schema.Attribute.Relation<
      'oneToMany',
      'api::core-contestant.core-contestant'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    latitude: Schema.Attribute.Decimal;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::core-school.core-school'
    > &
      Schema.Attribute.Private;
    longitude: Schema.Attribute.Decimal;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    postCode: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    redizo: Schema.Attribute.String;
    region: Schema.Attribute.Relation<
      'manyToOne',
      'api::core-region.core-region'
    >;
    schoolContacts: Schema.Attribute.Relation<
      'oneToMany',
      'api::core-school-contact.core-school-contact'
    >;
    schoolTypes: Schema.Attribute.Relation<
      'manyToMany',
      'api::core-school-type.core-school-type'
    >;
    shortName: Schema.Attribute.String;
    street: Schema.Attribute.String;
    town: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    web: Schema.Attribute.String;
  };
}

export interface ApiDummyDummy extends Struct.CollectionTypeSchema {
  collectionName: 'dummies';
  info: {
    displayName: 'dummy';
    pluralName: 'dummies';
    singularName: 'dummy';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::dummy.dummy'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    text: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiMailingBatchMailingBatch
  extends Struct.CollectionTypeSchema {
  collectionName: 'mailing_batches';
  info: {
    description: '';
    displayName: 'Mailing Batch';
    pluralName: 'mailing-batches';
    singularName: 'mailing-batch';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    author: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::users-permissions.user'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::mailing-batch.mailing-batch'
    > &
      Schema.Attribute.Private;
    mails: Schema.Attribute.Component<'mailing.list-of-sent-e-mails', true> &
      Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    status: Schema.Attribute.Enumeration<['completed', 'failed', 'wip']> &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'wip'>;
    template: Schema.Attribute.Relation<
      'oneToOne',
      'api::mailing-template.mailing-template'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiMailingBlacklistMailingBlacklist
  extends Struct.SingleTypeSchema {
  collectionName: 'mailing_blacklists';
  info: {
    description: '';
    displayName: 'Mailing Blacklist';
    pluralName: 'mailing-blacklists';
    singularName: 'mailing-blacklist';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::mailing-blacklist.mailing-blacklist'
    > &
      Schema.Attribute.Private;
    mails: Schema.Attribute.Component<'mailing.full-e-mail', true>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiMailingSourceMailingSource
  extends Struct.CollectionTypeSchema {
  collectionName: 'mailing_sources';
  info: {
    description: '';
    displayName: 'Mailing Source';
    pluralName: 'mailing-sources';
    singularName: 'mailing-source';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    author: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::users-permissions.user'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::mailing-source.mailing-source'
    > &
      Schema.Attribute.Private;
    mails: Schema.Attribute.Component<'mailing.full-e-mail', true> &
      Schema.Attribute.Required;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiMailingTemplateMailingTemplate
  extends Struct.CollectionTypeSchema {
  collectionName: 'mailing_templates';
  info: {
    description: '';
    displayName: 'Mailing Template';
    pluralName: 'mailing-templates';
    singularName: 'mailing-template';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    author: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::users-permissions.user'
    >;
    content: Schema.Attribute.Text &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::mailing-template.mailing-template'
    >;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    publishedAt: Schema.Attribute.DateTime;
    subject: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    theme: Schema.Attribute.Relation<
      'oneToOne',
      'api::mailing-theme.mailing-theme'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiMailingThemeMailingTheme
  extends Struct.CollectionTypeSchema {
  collectionName: 'mailing_themes';
  info: {
    description: '';
    displayName: 'Mailing Theme';
    pluralName: 'mailing-themes';
    singularName: 'mailing-theme';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    author: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::users-permissions.user'
    >;
    content: Schema.Attribute.Text;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::mailing-theme.mailing-theme'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    type: Schema.Attribute.Enumeration<['html', 'mjml']> &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'html'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiScgBotConfigScgBotConfig extends Struct.SingleTypeSchema {
  collectionName: 'scg_bot_configs';
  info: {
    description: '';
    displayName: 'SCG Bot Config';
    pluralName: 'scg-bot-configs';
    singularName: 'scg-bot-config';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    developers: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.user'
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::scg-bot-config.scg-bot-config'
    > &
      Schema.Attribute.Private;
    moderatorRoleId: Schema.Attribute.BigInteger;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    vvRoleId: Schema.Attribute.BigInteger;
  };
}

export interface ApiScgBotSuggestionScgBotSuggestion
  extends Struct.CollectionTypeSchema {
  collectionName: 'scg_bot_suggestions';
  info: {
    description: '';
    displayName: 'SCG Bot Suggestion';
    pluralName: 'scg-bot-suggestions';
    singularName: 'scg-bot-suggestion';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    additional: Schema.Attribute.Text;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::scg-bot-suggestion.scg-bot-suggestion'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    suggestion: Schema.Attribute.Text;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiTdaBotContestantTdaBotContestant
  extends Struct.CollectionTypeSchema {
  collectionName: 'tda_bot_contestants';
  info: {
    displayName: 'TdA Bot Contestant';
    pluralName: 'tda-bot-contestants';
    singularName: 'tda-bot-contestant';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-bot-contestant.tda-bot-contestant'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    team: Schema.Attribute.Relation<
      'oneToOne',
      'api::tda-core-team.tda-core-team'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    userId: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
  };
}

export interface ApiTdaBotTagTdaBotTag extends Struct.CollectionTypeSchema {
  collectionName: 'tda_bot_tags';
  info: {
    description: '';
    displayName: 'TdA Bot Tag';
    pluralName: 'tda-bot-tags';
    singularName: 'tda-bot-tag';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    content: Schema.Attribute.Text;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    keyphrase: Schema.Attribute.Text &
      Schema.Attribute.SetMinMaxLength<{
        maxLength: 2000;
      }>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-bot-tag.tda-bot-tag'
    > &
      Schema.Attribute.Private;
    modOnly: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<false>;
    name: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiTdaBotTdaBot extends Struct.SingleTypeSchema {
  collectionName: 'tda_bots';
  info: {
    description: '';
    displayName: 'TdA Bot Config';
    pluralName: 'tda-bots';
    singularName: 'tda-bot';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    adminRoleId: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'0'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    guildId: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'968050711097114624'>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-bot.tda-bot'
    > &
      Schema.Attribute.Private;
    modLogChannelId: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'0'>;
    modRoleId: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'0'>;
    noResponseMinHours: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          max: 168;
        },
        number
      > &
      Schema.Attribute.DefaultTo<72>;
    orgRoleId: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.DefaultTo<'1'>;
    publishedAt: Schema.Attribute.DateTime;
    statusText: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Schema.Attribute.DefaultTo<'sout\u011B\u017E\u00EDc\u00ED'>;
    statusType: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          max: 5;
          min: 0;
        },
        number
      > &
      Schema.Attribute.DefaultTo<1>;
    tagBannedUsers: Schema.Attribute.Component<'discord.discord-id-list', true>;
    techSupportRoleId: Schema.Attribute.Text &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'2'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiTdaBusherTeamTestTdaBusherTeamTest
  extends Struct.CollectionTypeSchema {
  collectionName: 'tda_busher_team_tests';
  info: {
    description: '';
    displayName: 'TdA Busher Team Test';
    pluralName: 'tda-busher-team-tests';
    singularName: 'tda-busher-team-test';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-busher-team-test.tda-busher-team-test'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    result: Schema.Attribute.Float &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          max: 1;
          min: 0;
        },
        number
      > &
      Schema.Attribute.DefaultTo<0>;
    team: Schema.Attribute.Relation<
      'oneToOne',
      'api::tda-core-team.tda-core-team'
    >;
    test: Schema.Attribute.Relation<
      'oneToOne',
      'api::tda-busher-test.tda-busher-test'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiTdaBusherTestTdaBusherTest
  extends Struct.CollectionTypeSchema {
  collectionName: 'tda_busher_tests';
  info: {
    description: '';
    displayName: 'TdA Busher Test';
    pluralName: 'tda-busher-tests';
    singularName: 'tda-busher-test';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.Text;
    endsAt: Schema.Attribute.DateTime & Schema.Attribute.Required;
    filename: Schema.Attribute.String & Schema.Attribute.Required;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-busher-test.tda-busher-test'
    > &
      Schema.Attribute.Private;
    maxPoints: Schema.Attribute.Integer &
      Schema.Attribute.SetMinMax<
        {
          max: 100;
          min: 0;
        },
        number
      > &
      Schema.Attribute.DefaultTo<0>;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    publishedAt: Schema.Attribute.DateTime;
    shortDescription: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        maxLength: 32;
      }>;
    startsAt: Schema.Attribute.DateTime & Schema.Attribute.Required;
    tda_phase: Schema.Attribute.Relation<
      'manyToOne',
      'api::tda-core-phase.tda-core-phase'
    >;
    type: Schema.Attribute.Enumeration<['bunTest', 'pyTest']> &
      Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiTdaCorePhaseTdaCorePhase
  extends Struct.CollectionTypeSchema {
  collectionName: 'tda_core_phases';
  info: {
    description: '';
    displayName: 'TdA CORE Phase';
    pluralName: 'tda-core-phases';
    singularName: 'tda-core-phase';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    endsAt: Schema.Attribute.DateTime & Schema.Attribute.Required;
    infoPanels: Schema.Attribute.Component<'tda-fisher-phases.info', true>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-core-phase.tda-core-phase'
    > &
      Schema.Attribute.Private;
    project: Schema.Attribute.Relation<
      'oneToOne',
      'api::core-project.core-project'
    >;
    publishedAt: Schema.Attribute.DateTime;
    startsAt: Schema.Attribute.DateTime & Schema.Attribute.Required;
    state: Schema.Attribute.Enumeration<
      [
        'undefined',
        'PRE_NK',
        'NK',
        'POST_NK',
        'PRE_SK',
        'SK',
        'POST_SK',
        'PRE_GRF',
        'GRF',
        'POST_GRF',
      ]
    > &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'undefined'>;
    tda_evaluation_sets: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-spechfish-evaluation-set.tda-spechfish-evaluation-set'
    >;
    tda_tests: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-busher-test.tda-busher-test'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiTdaCoreTeamTdaCoreTeam extends Struct.CollectionTypeSchema {
  collectionName: 'tda_core_teams';
  info: {
    description: '';
    displayName: 'TdA CORE Team';
    pluralName: 'tda-core-teams';
    singularName: 'tda-core-team';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    access: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<true>;
    allowedPartnerDataAccess: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<false>;
    contestants: Schema.Attribute.Relation<
      'oneToMany',
      'api::core-contestant.core-contestant'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    deployments: Schema.Attribute.Component<'tdc.deployment', true>;
    discordCode: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    ghostID: Schema.Attribute.Integer & Schema.Attribute.Unique;
    level: Schema.Attribute.Enumeration<['NK', 'SK', 'GRF']> &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'NK'>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-core-team.tda-core-team'
    > &
      Schema.Attribute.Private;
    meets: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-rezfish-meet.tda-rezfish-meet'
    >;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    ownReferral: Schema.Attribute.String &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        maxLength: 16;
        minLength: 10;
      }>;
    publishedAt: Schema.Attribute.DateTime;
    schoolContact: Schema.Attribute.Relation<
      'oneToOne',
      'api::core-school-contact.core-school-contact'
    >;
    slug: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    subdomain: Schema.Attribute.String & Schema.Attribute.Unique;
    tda_evaluation_teams: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-spechfish-evaluation-team.tda-spechfish-evaluation-team'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    url: Schema.Attribute.String & Schema.Attribute.Unique;
    usedReferral: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        maxLength: 16;
        minLength: 10;
      }>;
    uuid: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
  };
}

export interface ApiTdaFisherFlagTdaFisherFlag
  extends Struct.CollectionTypeSchema {
  collectionName: 'tda_fiher_flags';
  info: {
    description: '';
    displayName: 'TdA Fisher CTF Flag';
    pluralName: 'tda-fisher-flags';
    singularName: 'tda-fisher-flag';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    basePoints: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    flag: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.DefaultTo<'flag{}'>;
    help: Schema.Attribute.Text;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-fisher-flag.tda-fisher-flag'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        maxLength: 2;
        minLength: 1;
      }>;
    note: Schema.Attribute.DynamicZone<
      [
        'tda-ctf-components.email',
        'tda-ctf-components.generic-note',
        'tda-ctf-components.sticky-note',
      ]
    >;
    publishedAt: Schema.Attribute.DateTime;
    stage: Schema.Attribute.Enumeration<['NK', 'SK', 'GRF']> &
      Schema.Attribute.Required;
    typeOfNote: Schema.Attribute.Enumeration<
      ['generic', 'email', 'stickyNote', 'notepad']
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiTdaFisherTeamFlagTdaFisherTeamFlag
  extends Struct.CollectionTypeSchema {
  collectionName: 'tda_fisher_team_flags';
  info: {
    description: '';
    displayName: 'TdA Fisher Team Flag';
    pluralName: 'tda-fisher-team-flags';
    singularName: 'tda-fisher-team-flag';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    flag: Schema.Attribute.Relation<
      'oneToOne',
      'api::tda-fisher-flag.tda-fisher-flag'
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-fisher-team-flag.tda-fisher-team-flag'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    team: Schema.Attribute.Relation<
      'oneToOne',
      'api::tda-core-team.tda-core-team'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiTdaFisherTeamResultTdaFisherTeamResult
  extends Struct.CollectionTypeSchema {
  collectionName: 'tda_fisher_team_results';
  info: {
    description: '';
    displayName: 'TdA Fisher Team Results';
    pluralName: 'tda-fisher-team-results';
    singularName: 'tda-fisher-team-result';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    files: Schema.Attribute.Component<'tda-fisher-phases.result', true>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-fisher-team-result.tda-fisher-team-result'
    > &
      Schema.Attribute.Private;
    publishAt: Schema.Attribute.DateTime & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    tda_phase: Schema.Attribute.Relation<
      'oneToOne',
      'api::tda-core-phase.tda-core-phase'
    >;
    unpublishAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiTdaRezfishMeetCategoryTdaRezfishMeetCategory
  extends Struct.CollectionTypeSchema {
  collectionName: 'tda_rezfish_meet_categories';
  info: {
    description: '';
    displayName: 'TdA RezFish Meet Category';
    pluralName: 'tda-rezfish-meet-categories';
    singularName: 'tda-rezfish-meet-category';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-rezfish-meet-category.tda-rezfish-meet-category'
    > &
      Schema.Attribute.Private;
    maxMeets: Schema.Attribute.Integer;
    mentors: Schema.Attribute.Relation<
      'manyToMany',
      'api::tda-rezfish-mentor.tda-rezfish-mentor'
    >;
    minMeets: Schema.Attribute.Integer;
    name: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiTdaRezfishMeetLengthTdaRezfishMeetLength
  extends Struct.CollectionTypeSchema {
  collectionName: 'tda_rezfish_meet_lengths';
  info: {
    description: '';
    displayName: 'TdA RezFish Meet Length';
    pluralName: 'tda-rezfish-meet-lengths';
    singularName: 'tda-rezfish-meet-length';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    length: Schema.Attribute.Integer;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-rezfish-meet-length.tda-rezfish-meet-length'
    > &
      Schema.Attribute.Private;
    meets: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-rezfish-meet.tda-rezfish-meet'
    >;
    mentor: Schema.Attribute.Relation<
      'manyToOne',
      'api::tda-rezfish-mentor.tda-rezfish-mentor'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiTdaRezfishMeetTdaRezfishMeet
  extends Struct.CollectionTypeSchema {
  collectionName: 'tda_rezfish_meets';
  info: {
    description: '';
    displayName: 'TdA RezFish Meet';
    pluralName: 'tda-rezfish-meets';
    singularName: 'tda-rezfish-meet';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    date: Schema.Attribute.Date;
    endTime: Schema.Attribute.Time;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-rezfish-meet.tda-rezfish-meet'
    > &
      Schema.Attribute.Private;
    meet_length: Schema.Attribute.Relation<
      'manyToOne',
      'api::tda-rezfish-meet-length.tda-rezfish-meet-length'
    >;
    mentor: Schema.Attribute.Relation<
      'manyToOne',
      'api::tda-rezfish-mentor.tda-rezfish-mentor'
    >;
    publishedAt: Schema.Attribute.DateTime;
    startTime: Schema.Attribute.Time;
    tda_team: Schema.Attribute.Relation<
      'manyToOne',
      'api::tda-core-team.tda-core-team'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiTdaRezfishMentorTdaRezfishMentor
  extends Struct.CollectionTypeSchema {
  collectionName: 'tda_rezfish_mentors';
  info: {
    description: '';
    displayName: 'TdA RezFish Mentor';
    pluralName: 'tda-rezfish-mentors';
    singularName: 'tda-rezfish-mentor';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    availableTimes: Schema.Attribute.Component<
      'tda-rezfish-meet-components.available-time',
      false
    >;
    bio: Schema.Attribute.Text;
    created: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    email: Schema.Attribute.Email;
    firstname: Schema.Attribute.String;
    lastname: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-rezfish-mentor.tda-rezfish-mentor'
    > &
      Schema.Attribute.Private;
    meet_categories: Schema.Attribute.Relation<
      'manyToMany',
      'api::tda-rezfish-meet-category.tda-rezfish-meet-category'
    >;
    meet_lengths: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-rezfish-meet-length.tda-rezfish-meet-length'
    >;
    meets: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-rezfish-meet.tda-rezfish-meet'
    >;
    platform: Schema.Attribute.Relation<
      'manyToOne',
      'api::tda-rezfish-platform.tda-rezfish-platform'
    >;
    profilePic: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    publishedAt: Schema.Attribute.DateTime;
    tags: Schema.Attribute.Relation<
      'manyToMany',
      'api::tda-rezfish-tag.tda-rezfish-tag'
    >;
    token: Schema.Attribute.UID;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiTdaRezfishPlatformTdaRezfishPlatform
  extends Struct.CollectionTypeSchema {
  collectionName: 'tda_rezfish_platforms';
  info: {
    description: '';
    displayName: 'TdA RezFish Platform';
    pluralName: 'tda-rezfish-platforms';
    singularName: 'tda-rezfish-platform';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-rezfish-platform.tda-rezfish-platform'
    > &
      Schema.Attribute.Private;
    mentors: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-rezfish-mentor.tda-rezfish-mentor'
    >;
    name: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiTdaRezfishTagTdaRezfishTag
  extends Struct.CollectionTypeSchema {
  collectionName: 'tda_rezfish_tags';
  info: {
    description: '';
    displayName: 'TdA RezFish Tag';
    pluralName: 'tda-rezfish-tags';
    singularName: 'tda-rezfish-tag';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-rezfish-tag.tda-rezfish-tag'
    > &
      Schema.Attribute.Private;
    mentors: Schema.Attribute.Relation<
      'manyToMany',
      'api::tda-rezfish-mentor.tda-rezfish-mentor'
    >;
    publishedAt: Schema.Attribute.DateTime;
    tagName: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiTdaSpechfishEvaluationGroupTdaSpechfishEvaluationGroup
  extends Struct.CollectionTypeSchema {
  collectionName: 'tda_spechfish_evaluation_groups';
  info: {
    displayName: 'TdA SpechFish Evaluation Group';
    pluralName: 'tda-spechfish-evaluation-groups';
    singularName: 'tda-spechfish-evaluation-group';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    code: Schema.Attribute.String & Schema.Attribute.Required;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-spechfish-evaluation-group.tda-spechfish-evaluation-group'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    tda_evaluation_questions: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-spechfish-evaluation-question.tda-spechfish-evaluation-question'
    >;
    tda_evaluation_set: Schema.Attribute.Relation<
      'manyToOne',
      'api::tda-spechfish-evaluation-set.tda-spechfish-evaluation-set'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiTdaSpechfishEvaluationQuestionTdaSpechfishEvaluationQuestion
  extends Struct.CollectionTypeSchema {
  collectionName: 'tda_spechfish_evaluation_questions';
  info: {
    description: '';
    displayName: 'TdA SpechFish Evaluation Question';
    pluralName: 'tda-spechfish-evaluation-questions';
    singularName: 'tda-spechfish-evaluation-question';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    content: Schema.Attribute.RichText;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    isBonus: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-spechfish-evaluation-question.tda-spechfish-evaluation-question'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    order: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    points: Schema.Attribute.Float &
      Schema.Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      > &
      Schema.Attribute.DefaultTo<0>;
    publishedAt: Schema.Attribute.DateTime;
    tda_evaluation_group: Schema.Attribute.Relation<
      'manyToOne',
      'api::tda-spechfish-evaluation-group.tda-spechfish-evaluation-group'
    >;
    tda_evaluation_teams: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-spechfish-evaluation-team.tda-spechfish-evaluation-team'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiTdaSpechfishEvaluationSetTdaSpechfishEvaluationSet
  extends Struct.CollectionTypeSchema {
  collectionName: 'tda_spechfish_evaluation_sets';
  info: {
    description: '';
    displayName: 'TdA SpechFish Evaluation Set';
    pluralName: 'tda-spechfish-evaluation-sets';
    singularName: 'tda-spechfish-evaluation-set';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-spechfish-evaluation-set.tda-spechfish-evaluation-set'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    tda_evaluation_groups: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-spechfish-evaluation-group.tda-spechfish-evaluation-group'
    >;
    tda_phase: Schema.Attribute.Relation<
      'manyToOne',
      'api::tda-core-phase.tda-core-phase'
    >;
    type: Schema.Attribute.Enumeration<['FX', 'UX']> &
      Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiTdaSpechfishEvaluationTeamTdaSpechfishEvaluationTeam
  extends Struct.CollectionTypeSchema {
  collectionName: 'tda_spechfish_evaluation_teams';
  info: {
    description: '';
    displayName: 'TdA SpechFish Evaluation Team';
    pluralName: 'tda-spechfish-evaluation-teams';
    singularName: 'tda-spechfish-evaluation-team';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    gainedPoints: Schema.Attribute.Float &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-spechfish-evaluation-team.tda-spechfish-evaluation-team'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    tda_evaluation_question: Schema.Attribute.Relation<
      'manyToOne',
      'api::tda-spechfish-evaluation-question.tda-spechfish-evaluation-question'
    >;
    tda_team: Schema.Attribute.Relation<
      'manyToOne',
      'api::tda-core-team.tda-core-team'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    user: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::users-permissions.user'
    >;
  };
}

export interface ApiWebArticleCategoryWebArticleCategory
  extends Struct.CollectionTypeSchema {
  collectionName: 'web_article_categories';
  info: {
    description: '';
    displayName: 'Web Article Category';
    pluralName: 'web-article-categories';
    singularName: 'web-article-category';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    articles: Schema.Attribute.Relation<
      'oneToMany',
      'api::web-article.web-article'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    defaultCoverImage: Schema.Attribute.Media<'images'> &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::web-article-category.web-article-category'
    >;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiWebArticleWebArticle extends Struct.CollectionTypeSchema {
  collectionName: 'web_articles';
  info: {
    description: '';
    displayName: 'Web Article';
    pluralName: 'web-articles';
    singularName: 'web-article';
  };
  options: {
    draftAndPublish: true;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    anotation: Schema.Attribute.RichText &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    articleCategory: Schema.Attribute.Relation<
      'manyToOne',
      'api::web-article-category.web-article-category'
    >;
    author: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::users-permissions.user'
    >;
    content: Schema.Attribute.RichText &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    coverImage: Schema.Attribute.Media<'images'> &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    hasFullHtmlControl: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Schema.Attribute.DefaultTo<false>;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::web-article.web-article'
    >;
    publishDate: Schema.Attribute.DateTime &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    publishedAt: Schema.Attribute.DateTime;
    showAuthor: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Schema.Attribute.DefaultTo<true>;
    slug: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    title: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    unpublishDate: Schema.Attribute.DateTime &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiWebContactGroupWebContactGroup
  extends Struct.CollectionTypeSchema {
  collectionName: 'web_contact_groups';
  info: {
    displayName: 'Web Contact Group';
    pluralName: 'web-contact-groups';
    singularName: 'web-contact-group';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::web-contact-group.web-contact-group'
    >;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    webContacts: Schema.Attribute.Relation<
      'oneToMany',
      'api::web-contact.web-contact'
    >;
    weight: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Schema.Attribute.DefaultTo<0>;
  };
}

export interface ApiWebContactWebContact extends Struct.CollectionTypeSchema {
  collectionName: 'web_contacts';
  info: {
    description: '';
    displayName: 'Web Contact';
    pluralName: 'web-contacts';
    singularName: 'web-contact';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::web-contact.web-contact'
    >;
    publishedAt: Schema.Attribute.DateTime;
    roleName: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    user: Schema.Attribute.Relation<
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    webContactGroup: Schema.Attribute.Relation<
      'manyToOne',
      'api::web-contact-group.web-contact-group'
    >;
    weight: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }> &
      Schema.Attribute.DefaultTo<0>;
  };
}

export interface ApiWebMenuItemWebMenuItem extends Struct.CollectionTypeSchema {
  collectionName: 'web_menu_items';
  info: {
    description: '';
    displayName: 'Web Menu Item';
    pluralName: 'web-menu-items';
    singularName: 'web-menu-item';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    isValid: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }> &
      Schema.Attribute.DefaultTo<true>;
    link: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::web-menu-item.web-menu-item'
    >;
    menuType: Schema.Attribute.Enumeration<
      ['top_nav', 'front_page', 'footer_nav']
    > &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Schema.Attribute.DefaultTo<'top_nav'>;
    publishedAt: Schema.Attribute.DateTime;
    text: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    weight: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }> &
      Schema.Attribute.DefaultTo<0>;
  };
}

export interface ApiWebPageWebPage extends Struct.CollectionTypeSchema {
  collectionName: 'web_pages';
  info: {
    description: '';
    displayName: 'Web Page';
    pluralName: 'web-pages';
    singularName: 'web-page';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    blocks: Schema.Attribute.DynamicZone<
      [
        'content-blocks.home-splash-screen',
        'content-blocks.basic-content',
        'content-blocks.timed-content',
        'content-blocks.partners',
        'content-blocks.contacts',
        'content-blocks.registration-form',
        'content-blocks.registration-success',
        'content-blocks.article-list',
      ]
    > &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::web-page.web-page'
    >;
    metaDescription: Schema.Attribute.Text &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    noContentRedirectUrl: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    publishedAt: Schema.Attribute.DateTime;
    slug: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    title: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiWebPartnerGroupWebPartnerGroup
  extends Struct.CollectionTypeSchema {
  collectionName: 'web_partner_groups';
  info: {
    description: '';
    displayName: 'Web Partner Group';
    pluralName: 'web-partner-groups';
    singularName: 'web-partner-group';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    importance: Schema.Attribute.Enumeration<['high', 'medium', 'low']> &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Schema.Attribute.DefaultTo<'medium'>;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::web-partner-group.web-partner-group'
    >;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    webPartners: Schema.Attribute.Relation<
      'oneToMany',
      'api::web-partner.web-partner'
    >;
    weight: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Schema.Attribute.DefaultTo<0>;
  };
}

export interface ApiWebPartnerWebPartner extends Struct.CollectionTypeSchema {
  collectionName: 'web_partners';
  info: {
    description: '';
    displayName: 'Web Partner';
    pluralName: 'web-partners';
    singularName: 'web-partner';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::web-partner.web-partner'
    >;
    logo: Schema.Attribute.Media<'images'> &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    publishedAt: Schema.Attribute.DateTime;
    text: Schema.Attribute.Text &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    url: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    webPartnerGroup: Schema.Attribute.Relation<
      'manyToOne',
      'api::web-partner-group.web-partner-group'
    >;
    weight: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }> &
      Schema.Attribute.DefaultTo<0>;
  };
}

export interface ApiXoGhostProjectXoGhostProject
  extends Struct.CollectionTypeSchema {
  collectionName: 'xo_ghost_projects';
  info: {
    displayName: 'XO Ghost Project';
    pluralName: 'xo-ghost-projects';
    singularName: 'xo-ghost-project';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    ghostId: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::xo-ghost-project.xo-ghost-project'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    xo_ghost_teams: Schema.Attribute.Relation<
      'oneToMany',
      'api::xo-ghost-team.xo-ghost-team'
    >;
    xo_ghost_tournaments: Schema.Attribute.Relation<
      'oneToMany',
      'api::xo-ghost-tournament.xo-ghost-tournament'
    >;
  };
}

export interface ApiXoGhostSchoolXoGhostSchool
  extends Struct.CollectionTypeSchema {
  collectionName: 'xo_ghost_schools';
  info: {
    description: '';
    displayName: 'XO Ghost School';
    pluralName: 'xo-ghost-schools';
    singularName: 'xo-ghost-school';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    city: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    ghostTeam: Schema.Attribute.Relation<
      'oneToMany',
      'api::xo-ghost-team.xo-ghost-team'
    >;
    ghostTournaments: Schema.Attribute.Relation<
      'oneToMany',
      'api::xo-ghost-tournament.xo-ghost-tournament'
    >;
    lat: Schema.Attribute.Float;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::xo-ghost-school.xo-ghost-school'
    > &
      Schema.Attribute.Private;
    long: Schema.Attribute.Float;
    name: Schema.Attribute.String;
    postalCode: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    region: Schema.Attribute.String;
    shortName: Schema.Attribute.String;
    street: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiXoGhostTeamXoGhostTeam extends Struct.CollectionTypeSchema {
  collectionName: 'xo_ghost_teams';
  info: {
    description: '';
    displayName: 'XO Ghost team';
    pluralName: 'xo-ghost-teams';
    singularName: 'xo-ghost-team';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    ghostSchool: Schema.Attribute.Relation<
      'manyToOne',
      'api::xo-ghost-school.xo-ghost-school'
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::xo-ghost-team.xo-ghost-team'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String;
    project: Schema.Attribute.Relation<
      'manyToOne',
      'api::xo-ghost-project.xo-ghost-project'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiXoGhostTournamentXoGhostTournament
  extends Struct.CollectionTypeSchema {
  collectionName: 'xo_ghost_tournaments';
  info: {
    description: '';
    displayName: 'XO Ghost Tournament';
    pluralName: 'xo-ghost-tournaments';
    singularName: 'xo-ghost-tournament';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    ghostSchool: Schema.Attribute.Relation<
      'manyToOne',
      'api::xo-ghost-school.xo-ghost-school'
    >;
    groupOfActions: Schema.Attribute.String;
    koo: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::xo-ghost-tournament.xo-ghost-tournament'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String;
    places: Schema.Attribute.String;
    project: Schema.Attribute.Relation<
      'manyToOne',
      'api::xo-ghost-project.xo-ghost-project'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginContentReleasesRelease
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_releases';
  info: {
    displayName: 'Release';
    pluralName: 'releases';
    singularName: 'release';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    actions: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::content-releases.release-action'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::content-releases.release'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    releasedAt: Schema.Attribute.DateTime;
    scheduledAt: Schema.Attribute.DateTime;
    status: Schema.Attribute.Enumeration<
      ['ready', 'blocked', 'failed', 'done', 'empty']
    > &
      Schema.Attribute.Required;
    timezone: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginContentReleasesReleaseAction
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_release_actions';
  info: {
    displayName: 'Release Action';
    pluralName: 'release-actions';
    singularName: 'release-action';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    contentType: Schema.Attribute.String & Schema.Attribute.Required;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    entryDocumentId: Schema.Attribute.String;
    isEntryValid: Schema.Attribute.Boolean;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::content-releases.release-action'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    release: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::content-releases.release'
    >;
    type: Schema.Attribute.Enumeration<['publish', 'unpublish']> &
      Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginI18NLocale extends Struct.CollectionTypeSchema {
  collectionName: 'i18n_locale';
  info: {
    collectionName: 'locales';
    description: '';
    displayName: 'Locale';
    pluralName: 'locales';
    singularName: 'locale';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    code: Schema.Attribute.String & Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::i18n.locale'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.SetMinMax<
        {
          max: 50;
          min: 1;
        },
        number
      >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginReviewWorkflowsWorkflow
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_workflows';
  info: {
    description: '';
    displayName: 'Workflow';
    name: 'Workflow';
    pluralName: 'workflows';
    singularName: 'workflow';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    contentTypes: Schema.Attribute.JSON &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'[]'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::review-workflows.workflow'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    publishedAt: Schema.Attribute.DateTime;
    stageRequiredToPublish: Schema.Attribute.Relation<
      'oneToOne',
      'plugin::review-workflows.workflow-stage'
    >;
    stages: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::review-workflows.workflow-stage'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginReviewWorkflowsWorkflowStage
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_workflows_stages';
  info: {
    description: '';
    displayName: 'Stages';
    name: 'Workflow Stage';
    pluralName: 'workflow-stages';
    singularName: 'workflow-stage';
  };
  options: {
    draftAndPublish: false;
    version: '1.1.0';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    color: Schema.Attribute.String & Schema.Attribute.DefaultTo<'#4945FF'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::review-workflows.workflow-stage'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String;
    permissions: Schema.Attribute.Relation<'manyToMany', 'admin::permission'>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    workflow: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::review-workflows.workflow'
    >;
  };
}

export interface PluginStrapiPluginSsoRoles
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi-plugin-sso_roles';
  info: {
    collectionName: 'sso-roles';
    description: '';
    displayName: 'sso-role';
    pluralName: 'sso-roles';
    singularName: 'roles';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::strapi-plugin-sso.roles'
    > &
      Schema.Attribute.Private;
    oauth_type: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    roles: Schema.Attribute.JSON;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginStrapiPluginSsoWhitelists
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi-plugin-sso_whitelists';
  info: {
    collectionName: 'whitelists';
    description: '';
    displayName: 'whitelist';
    pluralName: 'whitelists';
    singularName: 'whitelists';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    email: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::strapi-plugin-sso.whitelists'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginUploadFile extends Struct.CollectionTypeSchema {
  collectionName: 'files';
  info: {
    description: '';
    displayName: 'File';
    pluralName: 'files';
    singularName: 'file';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    alternativeText: Schema.Attribute.String;
    caption: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    ext: Schema.Attribute.String;
    folder: Schema.Attribute.Relation<'manyToOne', 'plugin::upload.folder'> &
      Schema.Attribute.Private;
    folderPath: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Private &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    formats: Schema.Attribute.JSON;
    hash: Schema.Attribute.String & Schema.Attribute.Required;
    height: Schema.Attribute.Integer;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::upload.file'
    > &
      Schema.Attribute.Private;
    mime: Schema.Attribute.String & Schema.Attribute.Required;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    previewUrl: Schema.Attribute.String;
    provider: Schema.Attribute.String & Schema.Attribute.Required;
    provider_metadata: Schema.Attribute.JSON;
    publishedAt: Schema.Attribute.DateTime;
    related: Schema.Attribute.Relation<'morphToMany'>;
    size: Schema.Attribute.Decimal & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    url: Schema.Attribute.String & Schema.Attribute.Required;
    width: Schema.Attribute.Integer;
  };
}

export interface PluginUploadFolder extends Struct.CollectionTypeSchema {
  collectionName: 'upload_folders';
  info: {
    displayName: 'Folder';
    pluralName: 'folders';
    singularName: 'folder';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    children: Schema.Attribute.Relation<'oneToMany', 'plugin::upload.folder'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    files: Schema.Attribute.Relation<'oneToMany', 'plugin::upload.file'>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::upload.folder'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    parent: Schema.Attribute.Relation<'manyToOne', 'plugin::upload.folder'>;
    path: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    pathId: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginUsersPermissionsPermission
  extends Struct.CollectionTypeSchema {
  collectionName: 'up_permissions';
  info: {
    description: '';
    displayName: 'Permission';
    name: 'permission';
    pluralName: 'permissions';
    singularName: 'permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String & Schema.Attribute.Required;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.permission'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    role: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginUsersPermissionsRole
  extends Struct.CollectionTypeSchema {
  collectionName: 'up_roles';
  info: {
    description: '';
    displayName: 'Role';
    name: 'role';
    pluralName: 'roles';
    singularName: 'role';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.role'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    permissions: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.permission'
    >;
    publishedAt: Schema.Attribute.DateTime;
    type: Schema.Attribute.String & Schema.Attribute.Unique;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    users: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.user'
    >;
  };
}

export interface PluginUsersPermissionsUser
  extends Struct.CollectionTypeSchema {
  collectionName: 'up_users';
  info: {
    description: '';
    displayName: 'User';
    name: 'user';
    pluralName: 'users';
    singularName: 'user';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    address: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        maxLength: 200;
      }>;
    bankAccountNumber: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        maxLength: 35;
      }>;
    bio: Schema.Attribute.RichText;
    birthday: Schema.Attribute.Date;
    blocked: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    confirmationToken: Schema.Attribute.String & Schema.Attribute.Private;
    confirmed: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    discordId: Schema.Attribute.BigInteger;
    email: Schema.Attribute.Email &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    firstname: Schema.Attribute.String & Schema.Attribute.Required;
    isMember: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<false>;
    lastname: Schema.Attribute.String & Schema.Attribute.Required;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.user'
    > &
      Schema.Attribute.Private;
    mailing_batches: Schema.Attribute.Relation<
      'oneToMany',
      'api::mailing-batch.mailing-batch'
    >;
    mailing_sources: Schema.Attribute.Relation<
      'oneToMany',
      'api::mailing-source.mailing-source'
    >;
    mailing_templates: Schema.Attribute.Relation<
      'oneToMany',
      'api::mailing-template.mailing-template'
    >;
    mailing_themes: Schema.Attribute.Relation<
      'oneToMany',
      'api::mailing-theme.mailing-theme'
    >;
    nickname: Schema.Attribute.String;
    password: Schema.Attribute.Password &
      Schema.Attribute.Private &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    phoneNumber: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        maxLength: 20;
      }>;
    profilePictures: Schema.Attribute.Component<
      'multimedia-blocks.cropped-image',
      true
    >;
    provider: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    resetPasswordToken: Schema.Attribute.String & Schema.Attribute.Private;
    role: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    state: Schema.Attribute.Enumeration<
      [
        'Nov\u00E1\u010Dek',
        'Aktivn\u00ED',
        'Neaktivn\u00ED',
        'Alumni',
        'Nezn\u00E1mo',
      ]
    > &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'Nov\u00E1\u010Dek'>;
    tda_evaluation_teams: Schema.Attribute.Relation<
      'oneToMany',
      'api::tda-spechfish-evaluation-team.tda-spechfish-evaluation-team'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    username: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    webArticles: Schema.Attribute.Relation<
      'oneToMany',
      'api::web-article.web-article'
    >;
  };
}

declare module '@strapi/strapi' {
  export module Public {
    export interface ContentTypeSchemas {
      'admin::api-token': AdminApiToken;
      'admin::api-token-permission': AdminApiTokenPermission;
      'admin::permission': AdminPermission;
      'admin::role': AdminRole;
      'admin::transfer-token': AdminTransferToken;
      'admin::transfer-token-permission': AdminTransferTokenPermission;
      'admin::user': AdminUser;
      'api::core-contestant.core-contestant': ApiCoreContestantCoreContestant;
      'api::core-event-group.core-event-group': ApiCoreEventGroupCoreEventGroup;
      'api::core-event.core-event': ApiCoreEventCoreEvent;
      'api::core-project-type.core-project-type': ApiCoreProjectTypeCoreProjectType;
      'api::core-project.core-project': ApiCoreProjectCoreProject;
      'api::core-region.core-region': ApiCoreRegionCoreRegion;
      'api::core-school-contact.core-school-contact': ApiCoreSchoolContactCoreSchoolContact;
      'api::core-school-type.core-school-type': ApiCoreSchoolTypeCoreSchoolType;
      'api::core-school.core-school': ApiCoreSchoolCoreSchool;
      'api::dummy.dummy': ApiDummyDummy;
      'api::mailing-batch.mailing-batch': ApiMailingBatchMailingBatch;
      'api::mailing-blacklist.mailing-blacklist': ApiMailingBlacklistMailingBlacklist;
      'api::mailing-source.mailing-source': ApiMailingSourceMailingSource;
      'api::mailing-template.mailing-template': ApiMailingTemplateMailingTemplate;
      'api::mailing-theme.mailing-theme': ApiMailingThemeMailingTheme;
      'api::scg-bot-config.scg-bot-config': ApiScgBotConfigScgBotConfig;
      'api::scg-bot-suggestion.scg-bot-suggestion': ApiScgBotSuggestionScgBotSuggestion;
      'api::tda-bot-contestant.tda-bot-contestant': ApiTdaBotContestantTdaBotContestant;
      'api::tda-bot-tag.tda-bot-tag': ApiTdaBotTagTdaBotTag;
      'api::tda-bot.tda-bot': ApiTdaBotTdaBot;
      'api::tda-busher-team-test.tda-busher-team-test': ApiTdaBusherTeamTestTdaBusherTeamTest;
      'api::tda-busher-test.tda-busher-test': ApiTdaBusherTestTdaBusherTest;
      'api::tda-core-phase.tda-core-phase': ApiTdaCorePhaseTdaCorePhase;
      'api::tda-core-team.tda-core-team': ApiTdaCoreTeamTdaCoreTeam;
      'api::tda-fisher-flag.tda-fisher-flag': ApiTdaFisherFlagTdaFisherFlag;
      'api::tda-fisher-team-flag.tda-fisher-team-flag': ApiTdaFisherTeamFlagTdaFisherTeamFlag;
      'api::tda-fisher-team-result.tda-fisher-team-result': ApiTdaFisherTeamResultTdaFisherTeamResult;
      'api::tda-rezfish-meet-category.tda-rezfish-meet-category': ApiTdaRezfishMeetCategoryTdaRezfishMeetCategory;
      'api::tda-rezfish-meet-length.tda-rezfish-meet-length': ApiTdaRezfishMeetLengthTdaRezfishMeetLength;
      'api::tda-rezfish-meet.tda-rezfish-meet': ApiTdaRezfishMeetTdaRezfishMeet;
      'api::tda-rezfish-mentor.tda-rezfish-mentor': ApiTdaRezfishMentorTdaRezfishMentor;
      'api::tda-rezfish-platform.tda-rezfish-platform': ApiTdaRezfishPlatformTdaRezfishPlatform;
      'api::tda-rezfish-tag.tda-rezfish-tag': ApiTdaRezfishTagTdaRezfishTag;
      'api::tda-spechfish-evaluation-group.tda-spechfish-evaluation-group': ApiTdaSpechfishEvaluationGroupTdaSpechfishEvaluationGroup;
      'api::tda-spechfish-evaluation-question.tda-spechfish-evaluation-question': ApiTdaSpechfishEvaluationQuestionTdaSpechfishEvaluationQuestion;
      'api::tda-spechfish-evaluation-set.tda-spechfish-evaluation-set': ApiTdaSpechfishEvaluationSetTdaSpechfishEvaluationSet;
      'api::tda-spechfish-evaluation-team.tda-spechfish-evaluation-team': ApiTdaSpechfishEvaluationTeamTdaSpechfishEvaluationTeam;
      'api::web-article-category.web-article-category': ApiWebArticleCategoryWebArticleCategory;
      'api::web-article.web-article': ApiWebArticleWebArticle;
      'api::web-contact-group.web-contact-group': ApiWebContactGroupWebContactGroup;
      'api::web-contact.web-contact': ApiWebContactWebContact;
      'api::web-menu-item.web-menu-item': ApiWebMenuItemWebMenuItem;
      'api::web-page.web-page': ApiWebPageWebPage;
      'api::web-partner-group.web-partner-group': ApiWebPartnerGroupWebPartnerGroup;
      'api::web-partner.web-partner': ApiWebPartnerWebPartner;
      'api::xo-ghost-project.xo-ghost-project': ApiXoGhostProjectXoGhostProject;
      'api::xo-ghost-school.xo-ghost-school': ApiXoGhostSchoolXoGhostSchool;
      'api::xo-ghost-team.xo-ghost-team': ApiXoGhostTeamXoGhostTeam;
      'api::xo-ghost-tournament.xo-ghost-tournament': ApiXoGhostTournamentXoGhostTournament;
      'plugin::content-releases.release': PluginContentReleasesRelease;
      'plugin::content-releases.release-action': PluginContentReleasesReleaseAction;
      'plugin::i18n.locale': PluginI18NLocale;
      'plugin::review-workflows.workflow': PluginReviewWorkflowsWorkflow;
      'plugin::review-workflows.workflow-stage': PluginReviewWorkflowsWorkflowStage;
      'plugin::strapi-plugin-sso.roles': PluginStrapiPluginSsoRoles;
      'plugin::strapi-plugin-sso.whitelists': PluginStrapiPluginSsoWhitelists;
      'plugin::upload.file': PluginUploadFile;
      'plugin::upload.folder': PluginUploadFolder;
      'plugin::users-permissions.permission': PluginUsersPermissionsPermission;
      'plugin::users-permissions.role': PluginUsersPermissionsRole;
      'plugin::users-permissions.user': PluginUsersPermissionsUser;
    }
  }
}
